import { <PERSON><PERSON>Db, <PERSON><PERSON>BookingDb, CardDb, AdhyayanFeedback } from '../models/associations.js';
import { STATUS_CONFIRMED, STATUS_CASH_COMPLETED } from '../config/constants.js';
import { sendNotification } from './sendNotification.helper.js';
import sendMail from '../utils/sendMail.js';
import moment from 'moment';
import Sequelize from 'sequelize';
import logger from '../config/logger.js';

/**
 * Triggers feedback collection for completed adhyayans
 * This function should be called daily via cron job
 */
export async function triggerFeedbackCollection() {
  try {
    logger.info('Starting feedback collection trigger process');
    
    // Get adhyayans that ended yesterday (to give a day buffer)
    const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');
    
    const completedAdhyayans = await ShibirDb.findAll({
      where: {
        end_date: yesterday
      },
      include: [
        {
          model: <PERSON><PERSON>BookingDb,
          where: {
            status: [STATUS_CONFIRMED, STATUS_CASH_COMPLETED]
          },
          include: [
            {
              model: CardDb,
              attributes: ['cardno', 'issuedto', 'email', 'mobno']
            }
          ]
        }
      ]
    });

    logger.info(`Found ${completedAdhyayans.length} completed adhyayans for feedback collection`);

    for (const adhyayan of completedAdhyayans) {
      await processFeedbackForAdhyayan(adhyayan);
    }

    logger.info('Feedback collection trigger process completed');
  } catch (error) {
    logger.error('Error in feedback collection trigger:', error);
    throw error;
  }
}

/**
 * Processes feedback collection for a specific adhyayan
 * @param {Object} adhyayan - Adhyayan object with bookings
 */
async function processFeedbackForAdhyayan(adhyayan) {
  try {
    logger.info(`Processing feedback collection for adhyayan: ${adhyayan.name} (ID: ${adhyayan.id})`);
    
    const participants = [];
    
    // Get all confirmed participants
    for (const booking of adhyayan.ShibirBookingDbs) {
      const cardno = booking.cardno;
      
      // Check if feedback already submitted
      const existingFeedback = await AdhyayanFeedback.findOne({
        where: {
          cardno: cardno,
          shibir_id: adhyayan.id
        }
      });

      if (!existingFeedback) {
        participants.push({
          cardno: cardno,
          name: booking.CardDb?.issuedto || 'Participant',
          email: booking.CardDb?.email,
          mobno: booking.CardDb?.mobno
        });
      }
    }

    logger.info(`Found ${participants.length} participants eligible for feedback for adhyayan ${adhyayan.id}`);

    // Send feedback requests to eligible participants
    for (const participant of participants) {
      await sendFeedbackRequest(adhyayan, participant);
    }

  } catch (error) {
    logger.error(`Error processing feedback for adhyayan ${adhyayan.id}:`, error);
  }
}

/**
 * Sends feedback request to a participant
 * @param {Object} adhyayan - Adhyayan object
 * @param {Object} participant - Participant object
 */
async function sendFeedbackRequest(adhyayan, participant) {
  try {
    // Send email notification
    if (participant.email) {
      await sendFeedbackEmail(adhyayan, participant);
    }

    // Send push notification if mobile number exists
    if (participant.mobno) {
      await sendFeedbackNotification(adhyayan, participant);
    }

    logger.info(`Feedback request sent to ${participant.name} (${participant.cardno}) for adhyayan ${adhyayan.id}`);
  } catch (error) {
    logger.error(`Error sending feedback request to ${participant.cardno}:`, error);
  }
}

/**
 * Sends feedback request email
 * @param {Object} adhyayan - Adhyayan object
 * @param {Object} participant - Participant object
 */
async function sendFeedbackEmail(adhyayan, participant) {
  const subject = `Feedback Request: ${adhyayan.name}`;
  
  const emailContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Thank you for attending ${adhyayan.name}</h2>
      
      <p>Dear ${participant.name},</p>
      
      <p>We hope you had a wonderful experience at the recent Raj Adhyayan session:</p>
      
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <strong>Session Details:</strong><br>
        <strong>Name:</strong> ${adhyayan.name}<br>
        <strong>Speaker:</strong> ${adhyayan.speaker}<br>
        <strong>Dates:</strong> ${moment(adhyayan.start_date).format('DD MMM YYYY')} - ${moment(adhyayan.end_date).format('DD MMM YYYY')}<br>
        <strong>Location:</strong> ${adhyayan.location}
      </div>
      
      <p>Your feedback is invaluable to us and helps us improve our future sessions. Please take a few minutes to share your experience by submitting feedback through the Aashray app.</p>
      
      <p>The feedback form covers:</p>
      <ul>
        <li>Rating for the Swadhay Karta's session</li>
        <li>Your personal interaction experience</li>
        <li>Suggestions for improvement</li>
        <li>Interest in future sessions</li>
        <li>Food and accommodation experience</li>
      </ul>
      
      <p>Thank you for your time and participation.</p>
      
      <p>With gratitude,<br>
      Vitraag Vigyaan Aashray Team</p>
    </div>
  `;

  await sendMail({
    email: participant.email,
    subject: subject,
    html: emailContent
  });
}

/**
 * Sends feedback request push notification
 * @param {Object} adhyayan - Adhyayan object
 * @param {Object} participant - Participant object
 */
async function sendFeedbackNotification(adhyayan, participant) {
  const title = 'Feedback Request';
  const message = `Please share your feedback for ${adhyayan.name}. Your input helps us improve future sessions.`;
  
  await sendNotification({
    cardno: participant.cardno,
    title: title,
    message: message,
    data: {
      type: 'feedback_request',
      shibir_id: adhyayan.id,
      adhyayan_name: adhyayan.name
    }
  });
}

/**
 * Manually trigger feedback collection for a specific adhyayan
 * This can be called from admin interface
 * @param {number} shibir_id - Adhyayan ID
 */
export async function triggerFeedbackForAdhyayan(shibir_id) {
  try {
    const adhyayan = await ShibirDb.findOne({
      where: { id: shibir_id },
      include: [
        {
          model: ShibirBookingDb,
          where: {
            status: [STATUS_CONFIRMED, STATUS_CASH_COMPLETED]
          },
          include: [
            {
              model: CardDb,
              attributes: ['cardno', 'issuedto', 'email', 'mobno']
            }
          ]
        }
      ]
    });

    if (!adhyayan) {
      throw new Error(`Adhyayan with ID ${shibir_id} not found`);
    }

    // Check if adhyayan has ended
    const today = moment().format('YYYY-MM-DD');
    if (moment(adhyayan.end_date).isAfter(today)) {
      throw new Error('Cannot trigger feedback for ongoing or future adhyayan');
    }

    await processFeedbackForAdhyayan(adhyayan);
    
    logger.info(`Manual feedback trigger completed for adhyayan ${shibir_id}`);
    return { success: true, message: 'Feedback collection triggered successfully' };
  } catch (error) {
    logger.error(`Error in manual feedback trigger for adhyayan ${shibir_id}:`, error);
    throw error;
  }
}

/**
 * Gets feedback collection statistics
 * @param {number} shibir_id - Adhyayan ID
 * @returns {Object} - Statistics object
 */
export async function getFeedbackCollectionStats(shibir_id) {
  const adhyayan = await ShibirDb.findOne({
    where: { id: shibir_id }
  });

  if (!adhyayan) {
    throw new Error(`Adhyayan with ID ${shibir_id} not found`);
  }

  // Get total confirmed participants
  const totalParticipants = await ShibirBookingDb.count({
    where: {
      shibir_id: shibir_id,
      status: [STATUS_CONFIRMED, STATUS_CASH_COMPLETED]
    }
  });

  // Get feedback submissions
  const feedbackSubmissions = await AdhyayanFeedback.count({
    where: { shibir_id: shibir_id }
  });

  // Calculate response rate
  const responseRate = totalParticipants > 0 ? 
    Math.round((feedbackSubmissions / totalParticipants) * 100) : 0;

  return {
    adhyayan_name: adhyayan.name,
    total_participants: totalParticipants,
    feedback_submissions: feedbackSubmissions,
    pending_feedback: totalParticipants - feedbackSubmissions,
    response_rate: responseRate
  };
}
