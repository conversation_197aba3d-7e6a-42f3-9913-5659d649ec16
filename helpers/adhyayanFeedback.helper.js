import { AdhyayanFeedback, <PERSON>birDb, CardDb, <PERSON><PERSON>BookingDb } from '../models/associations.js';
import {
  STATUS_CONFIRMED,
  STATUS_CASH_COMPLETED,
  ERR_FEEDBACK_ALREADY_SUBMITTED,
  ERR_ADHYAYAN_NOT_FOUND,
  ERR_FEEDBACK_NOT_ALLOWED,
  ERR_ADHYAYAN_NOT_COMPLETED
} from '../config/constants.js';
import ApiError from '../utils/ApiError.js';
import moment from 'moment';
import Sequelize from 'sequelize';

/**
 * Validates if user can submit feedback for an adhyayan
 * @param {string} cardno - User's card number
 * @param {number} shibir_id - Adhyayan ID
 * @returns {Promise<Object>} - Validation result with adhyayan and booking info
 */
export async function validateFeedbackEligibility(cardno, shibir_id) {
  // Check if adhyayan exists
  const adhyayan = await ShibirDb.findOne({
    where: { id: shibir_id }
  });

  if (!adhyayan) {
    throw new ApiError(404, ERR_ADHYAYAN_NOT_FOUND);
  }

  // Check if adhyayan has ended
  const today = moment().format('YYYY-MM-DD');
  if (moment(adhyayan.end_date).isAfter(today)) {
    throw new ApiError(400, ERR_ADHYAYAN_NOT_COMPLETED);
  }

  // Check if user has a confirmed booking for this adhyayan
  const booking = await ShibirBookingDb.findOne({
    where: {
      [Sequelize.Op.or]: [
        { cardno: cardno },
        { bookedBy: cardno }
      ],
      shibir_id: shibir_id,
      status: [STATUS_CONFIRMED, STATUS_CASH_COMPLETED]
    }
  });

  if (!booking) {
    throw new ApiError(403, ERR_FEEDBACK_NOT_ALLOWED);
  }

  // Check if feedback already submitted
  const existingFeedback = await AdhyayanFeedback.findOne({
    where: {
      cardno: cardno,
      shibir_id: shibir_id
    }
  });

  if (existingFeedback) {
    throw new ApiError(400, ERR_FEEDBACK_ALREADY_SUBMITTED);
  }

  return { adhyayan, booking };
}

/**
 * Validates feedback data
 * @param {Object} feedbackData - Feedback form data
 * @returns {Object} - Validated feedback data
 */
export function validateFeedbackData(feedbackData) {
  const {
    swadhay_karta_rating,
    personal_interaction_rating,
    swadhay_karta_suggestions,
    raj_adhyayan_interest,
    future_topics,
    loved_most,
    improvement_suggestions,
    food_rating,
    stay_rating
  } = feedbackData;

  // Validate required rating fields
  const ratings = [
    { field: 'swadhay_karta_rating', value: swadhay_karta_rating },
    { field: 'personal_interaction_rating', value: personal_interaction_rating },
    { field: 'food_rating', value: food_rating },
    { field: 'stay_rating', value: stay_rating }
  ];

  for (const rating of ratings) {
    if (!rating.value || rating.value < 1 || rating.value > 5) {
      throw new ApiError(400, `${rating.field} must be between 1 and 5`);
    }
  }

  // Validate boolean field
  if (typeof raj_adhyayan_interest !== 'boolean') {
    throw new ApiError(400, 'raj_adhyayan_interest must be true or false');
  }

  // Validate text field lengths
  const textFields = [
    { field: 'swadhay_karta_suggestions', value: swadhay_karta_suggestions },
    { field: 'future_topics', value: future_topics },
    { field: 'loved_most', value: loved_most },
    { field: 'improvement_suggestions', value: improvement_suggestions }
  ];

  for (const textField of textFields) {
    if (textField.value && textField.value.length > 1000) {
      throw new ApiError(400, `${textField.field} must be less than 1000 characters`);
    }
  }

  return {
    swadhay_karta_rating: parseInt(swadhay_karta_rating),
    personal_interaction_rating: parseInt(personal_interaction_rating),
    swadhay_karta_suggestions: swadhay_karta_suggestions?.trim() || null,
    raj_adhyayan_interest: Boolean(raj_adhyayan_interest),
    future_topics: future_topics?.trim() || null,
    loved_most: loved_most?.trim() || null,
    improvement_suggestions: improvement_suggestions?.trim() || null,
    food_rating: parseInt(food_rating),
    stay_rating: parseInt(stay_rating)
  };
}

/**
 * Creates feedback record
 * @param {string} cardno - User's card number
 * @param {number} shibir_id - Adhyayan ID
 * @param {Object} feedbackData - Validated feedback data
 * @param {Object} transaction - Database transaction
 * @returns {Promise<Object>} - Created feedback record
 */
export async function createFeedback(cardno, shibir_id, feedbackData, transaction) {
  const feedback = await AdhyayanFeedback.create({
    cardno,
    shibir_id,
    ...feedbackData,
    updatedBy: cardno
  }, { transaction });

  return feedback;
}

/**
 * Gets feedback statistics for an adhyayan
 * @param {number} shibir_id - Adhyayan ID
 * @returns {Promise<Object>} - Feedback statistics
 */
export async function getFeedbackStats(shibir_id) {
  const stats = await AdhyayanFeedback.findAll({
    where: { shibir_id },
    attributes: [
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'total_responses'],
      [Sequelize.fn('AVG', Sequelize.col('swadhay_karta_rating')), 'avg_swadhay_karta_rating'],
      [Sequelize.fn('AVG', Sequelize.col('personal_interaction_rating')), 'avg_personal_interaction_rating'],
      [Sequelize.fn('AVG', Sequelize.col('food_rating')), 'avg_food_rating'],
      [Sequelize.fn('AVG', Sequelize.col('stay_rating')), 'avg_stay_rating'],
      [Sequelize.fn('SUM', Sequelize.literal('CASE WHEN raj_adhyayan_interest = 1 THEN 1 ELSE 0 END')), 'interested_in_future']
    ],
    raw: true
  });

  return stats[0];
}

/**
 * Checks if user has already submitted feedback for an adhyayan
 * @param {string} cardno - User's card number
 * @param {number} shibir_id - Adhyayan ID
 * @returns {Promise<boolean>} - True if feedback exists
 */
export async function hasFeedbackSubmitted(cardno, shibir_id) {
  const feedback = await AdhyayanFeedback.findOne({
    where: {
      cardno,
      shibir_id
    }
  });

  return !!feedback;
}

/**
 * Gets list of completed adhyayans for which user can submit feedback
 * @param {string} cardno - User's card number
 * @returns {Promise<Array>} - List of eligible adhyayans
 */
export async function getEligibleAdhyayansForFeedback(cardno) {
  const today = moment().format('YYYY-MM-DD');
  
  const eligibleAdhyayans = await ShibirDb.findAll({
    include: [
      {
        model: ShibirBookingDb,
        where: {
          [Sequelize.Op.or]: [
            { cardno: cardno },
            { bookedBy: cardno }
          ],
          status: [STATUS_CONFIRMED, STATUS_CASH_COMPLETED]
        },
        required: true
      },
      {
        model: AdhyayanFeedback,
        where: { cardno: cardno },
        required: false
      }
    ],
    where: {
      end_date: {
        [Sequelize.Op.lt]: today
      }
    }
  });

  // Filter out adhyayans where feedback is already submitted
  return eligibleAdhyayans.filter(adhyayan => 
    !adhyayan.AdhyayanFeedbacks || adhyayan.AdhyayanFeedbacks.length === 0
  );
}
