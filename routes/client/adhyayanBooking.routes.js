import express from 'express';
const router = express.Router();
import {
  FetchAllShibir,
  FetchBookedShibir,
  CancelShibir,
  FetchShibirInRange,
  FetchShibirById,
  submitFeedback,
  getUserFeedback,
  getEligibleAdhyayans,
  getFeedbackStatus
} from '../../controllers/client/adhyayanBooking.controller.js';
import { validateCard } from '../../middleware/validate.js';
import {
  validateFeedbackSubmission,
  validateShibirIdParam
} from '../../middleware/feedbackValidation.js';
import CatchAsync from '../../utils/CatchAsync.js';

router.use(validateCard);

router.get('/getall', CatchAsync(FetchAllShibir));
router.get('/getbooked', CatchAsync(FetchBookedShibir));
router.delete('/cancel', CatchAsync(CancelShibir));
router.get('/getrange', CatchAsync(FetchShibirInRange));
router.get('/:id', CatchAsync(FetchShibirById));

router.post(
  '/feedback',
  validateFeedbackSubmission,
  CatchAsync(submitFeedback)
);
router.get('/:shibir_id', validateShibirIdParam, CatchAsync(getUserFeedback));
router.get('/eligible', CatchAsync(getEligibleAdhyayans));
router.get(
  '/status/:shibir_id',
  validateShibirIdParam,
  CatchAsync(getFeedbackStatus)
);

export default router;
