import express from 'express';
const router = express.Router();
import {
  submitFeedback,
  getEligibleAdhyayans,
  getFeedbackStatus,
  getUserFeedback
} from '../../controllers/client/adhyayanFeedback.controller.js';
import { validateCard } from '../../middleware/validate.js';
import {
  validateFeedbackSubmission,
  validateShibirIdParam
} from '../../middleware/feedbackValidation.js';
import CatchAsync from '../../utils/CatchAsync.js';

// All routes require authentication
router.use(validateCard);

// Submit feedback for an adhyayan
router.post('/submit', validateFeedbackSubmission, CatchAsync(submitFeedback));

// Get list of adhyayans eligible for feedback
router.get('/eligible', CatchAsync(getEligibleAdhyayans));

// Check feedback status for a specific adhyayan
router.get(
  '/status/:shibir_id',
  validateShibirIdParam,
  CatchAsync(getFeedbackStatus)
);

// Get user's submitted feedback for a specific adhyayan
router.get('/:shibir_id', validate<PERSON><PERSON><PERSON>IdParam, CatchAsync(getUserFeedback));

export default router;
