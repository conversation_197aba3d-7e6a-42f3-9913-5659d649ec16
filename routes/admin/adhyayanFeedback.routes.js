import express from 'express';
const router = express.Router();
import {
  getAdhyayanFeedback,
  getFeedbackSummary,
  getFeedbackReport,
  getFeedbackAnalytics,
  triggerFeedbackCollection,
  fetchFeedbackStats
} from '../../controllers/admin/adhyayanFeedback.controller.js';
import { auth, authorizeRoles } from '../../middleware/AdminAuth.js';
import {
  ROLE_SUPER_ADMIN,
  ROLE_ADHYAYAN_ADMIN
} from '../../config/constants.js';
import {
  validateFeedbackQuery,
  validateShibirIdParam
} from '../../middleware/feedbackValidation.js';
import CatchAsync from '../../utils/CatchAsync.js';

// All routes require admin authentication
router.use(auth);
router.use(authorizeRoles(ROLE_SUPER_ADMIN, ROLE_ADHYAYAN_ADMIN));

// Get feedback analytics dashboard data
router.get('/analytics', CatchAsync(getFeedbackAnalytics));

// Get feedback summary for all adhyayans
router.get('/summary', validateFeedbackQuery, CatchAsync(getFeedbackSummary));

// Get all feedback for a specific adhyayan
router.get(
  '/adhyayan/:shibir_id',
  validateShibirIdParam,
  validateFeedbackQuery,
  CatchAsync(getAdhyayanFeedback)
);

// Get detailed feedback report for export
router.get(
  '/report/:shibir_id',
  validateShibirIdParam,
  CatchAsync(getFeedbackReport)
);

// Manually trigger feedback collection for an adhyayan
router.post(
  '/trigger/:shibir_id',
  validateShibirIdParam,
  CatchAsync(triggerFeedbackCollection)
);

// Get feedback collection statistics for an adhyayan
router.get(
  '/stats/:shibir_id',
  validateShibirIdParam,
  CatchAsync(fetchFeedbackStats)
);

export default router;
