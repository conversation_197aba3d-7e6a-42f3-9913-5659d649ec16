import {
  AdhyayanFeedback,
  ShibirDb,
  CardDb
} from '../../models/associations.js';
import { getFeedbackStats } from '../../helpers/adhyayanFeedback.helper.js';
import {
  triggerFeedbackFor<PERSON><PERSON><PERSON><PERSON>,
  getFeedbackCollectionStats
} from '../../helpers/feedbackTrigger.helper.js';
import {
  MSG_FETCH_SUCCESSFUL,
  MSG_UPDATE_SUCCESSFUL
} from '../../config/constants.js';
import database from '../../config/database.js';
import { QueryTypes } from 'sequelize';
import ApiError from '../../utils/ApiError.js';
import moment from 'moment';

/**
 * Get all feedback for a specific adhyayan
 * GET /api/v1/admin/feedback/adhyayan/:shibir_id
 */
export const getAdhyayanFeedback = async (req, res) => {
  const { shibir_id } = req.params;
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.page_size) || 20;
  const offset = (page - 1) * pageSize;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  // Get feedback with user details
  const feedback = await AdhyayanFeedback.findAll({
    where: { shibir_id: parseInt(shibir_id) },
    include: [
      {
        model: CardDb,
        attributes: ['cardno', 'issuedto', 'center', 'res_status']
      },
      {
        model: ShibirDb,
        attributes: [
          'id',
          'name',
          'speaker',
          'start_date',
          'end_date',
          'location'
        ]
      }
    ],
    order: [['submitted_at', 'DESC']],
    offset,
    limit: pageSize
  });

  // Get total count
  const totalCount = await AdhyayanFeedback.count({
    where: { shibir_id: parseInt(shibir_id) }
  });

  // Get feedback statistics
  const stats = await getFeedbackStats(parseInt(shibir_id));

  return res.status(200).send({
    message: MSG_FETCH_SUCCESSFUL,
    data: {
      feedback,
      stats,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    }
  });
};

/**
 * Get feedback summary for all adhyayans
 * GET /api/v1/admin/feedback/summary
 */
export const getFeedbackSummary = async (req, res) => {
  const { start_date, end_date, location } = req.query;

  let whereClause = '';
  const replacements = {};

  if (start_date && end_date) {
    whereClause += ' AND s.start_date BETWEEN :start_date AND :end_date';
    replacements.start_date = start_date;
    replacements.end_date = end_date;
  }

  if (location) {
    whereClause += ' AND s.location = :location';
    replacements.location = location;
  }

  const summary = await database.query(
    `SELECT 
      s.id,
      s.name,
      s.speaker,
      s.start_date,
      s.end_date,
      s.location,
      s.total_seats,
      COUNT(DISTINCT sb.cardno) as total_attendees,
      COUNT(af.id) as feedback_count,
      ROUND(AVG(af.swadhay_karta_rating), 2) as avg_swadhay_karta_rating,
      ROUND(AVG(af.personal_interaction_rating), 2) as avg_personal_interaction_rating,
      ROUND(AVG(af.food_rating), 2) as avg_food_rating,
      ROUND(AVG(af.stay_rating), 2) as avg_stay_rating,
      SUM(CASE WHEN af.raj_adhyayan_interest = 1 THEN 1 ELSE 0 END) as interested_in_future,
      ROUND((COUNT(af.id) * 100.0 / COUNT(DISTINCT sb.cardno)), 2) as response_rate
    FROM shibir_db s
    LEFT JOIN shibir_booking_db sb ON s.id = sb.shibir_id 
      AND sb.status IN ('confirmed', 'cash completed')
    LEFT JOIN adhyayan_feedback af ON s.id = af.shibir_id
    WHERE s.end_date < CURRENT_DATE ${whereClause}
    GROUP BY s.id, s.name, s.speaker, s.start_date, s.end_date, s.location, s.total_seats
    HAVING COUNT(DISTINCT sb.cardno) > 0
    ORDER BY s.start_date DESC`,
    {
      replacements,
      type: QueryTypes.SELECT
    }
  );

  return res.status(200).send({
    message: MSG_FETCH_SUCCESSFUL,
    data: summary
  });
};

/**
 * Get detailed feedback report for export
 * GET /api/v1/admin/feedback/report/:shibir_id
 */
export const getFeedbackReport = async (req, res) => {
  const { shibir_id } = req.params;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  const report = await database.query(
    `SELECT 
      s.name as adhyayan_name,
      s.speaker,
      s.start_date,
      s.end_date,
      s.location,
      c.cardno,
      c.issuedto as participant_name,
      c.center,
      c.res_status,
      af.swadhay_karta_rating,
      af.personal_interaction_rating,
      af.swadhay_karta_suggestions,
      af.raj_adhyayan_interest,
      af.future_topics,
      af.loved_most,
      af.improvement_suggestions,
      af.food_rating,
      af.stay_rating,
      af.submitted_at
    FROM adhyayan_feedback af
    JOIN shibir_db s ON af.shibir_id = s.id
    JOIN card_db c ON af.cardno = c.cardno
    WHERE af.shibir_id = :shibir_id
    ORDER BY af.submitted_at DESC`,
    {
      replacements: { shibir_id: parseInt(shibir_id) },
      type: QueryTypes.SELECT
    }
  );

  return res.status(200).send({
    message: MSG_FETCH_SUCCESSFUL,
    data: report
  });
};

/**
 * Get feedback analytics dashboard data
 * GET /api/v1/admin/feedback/analytics
 */
export const getFeedbackAnalytics = async (req, res) => {
  const { period = '6months' } = req.query;

  let dateFilter = '';
  if (period === '1month') {
    dateFilter = 'AND s.start_date >= DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH)';
  } else if (period === '3months') {
    dateFilter = 'AND s.start_date >= DATE_SUB(CURRENT_DATE, INTERVAL 3 MONTH)';
  } else if (period === '6months') {
    dateFilter = 'AND s.start_date >= DATE_SUB(CURRENT_DATE, INTERVAL 6 MONTH)';
  } else if (period === '1year') {
    dateFilter = 'AND s.start_date >= DATE_SUB(CURRENT_DATE, INTERVAL 1 YEAR)';
  }

  // Overall statistics
  const overallStats = await database.query(
    `SELECT 
      COUNT(DISTINCT af.shibir_id) as total_adhyayans_with_feedback,
      COUNT(af.id) as total_feedback_responses,
      ROUND(AVG(af.swadhay_karta_rating), 2) as avg_swadhay_karta_rating,
      ROUND(AVG(af.personal_interaction_rating), 2) as avg_personal_interaction_rating,
      ROUND(AVG(af.food_rating), 2) as avg_food_rating,
      ROUND(AVG(af.stay_rating), 2) as avg_stay_rating,
      SUM(CASE WHEN af.raj_adhyayan_interest = 1 THEN 1 ELSE 0 END) as total_interested_in_future
    FROM adhyayan_feedback af
    JOIN shibir_db s ON af.shibir_id = s.id
    WHERE s.end_date < CURRENT_DATE ${dateFilter}`,
    {
      type: QueryTypes.SELECT
    }
  );

  // Monthly trend
  const monthlyTrend = await database.query(
    `SELECT 
      DATE_FORMAT(s.start_date, '%Y-%m') as month,
      COUNT(af.id) as feedback_count,
      ROUND(AVG(af.swadhay_karta_rating), 2) as avg_swadhay_karta_rating,
      ROUND(AVG(af.food_rating), 2) as avg_food_rating
    FROM adhyayan_feedback af
    JOIN shibir_db s ON af.shibir_id = s.id
    WHERE s.end_date < CURRENT_DATE ${dateFilter}
    GROUP BY DATE_FORMAT(s.start_date, '%Y-%m')
    ORDER BY month DESC`,
    {
      type: QueryTypes.SELECT
    }
  );

  // Location-wise statistics
  const locationStats = await database.query(
    `SELECT 
      s.location,
      COUNT(af.id) as feedback_count,
      ROUND(AVG(af.swadhay_karta_rating), 2) as avg_swadhay_karta_rating,
      ROUND(AVG(af.food_rating), 2) as avg_food_rating,
      ROUND(AVG(af.stay_rating), 2) as avg_stay_rating
    FROM adhyayan_feedback af
    JOIN shibir_db s ON af.shibir_id = s.id
    WHERE s.end_date < CURRENT_DATE ${dateFilter}
    GROUP BY s.location
    ORDER BY feedback_count DESC`,
    {
      type: QueryTypes.SELECT
    }
  );

  return res.status(200).send({
    message: MSG_FETCH_SUCCESSFUL,
    data: {
      overall: overallStats[0],
      monthly_trend: monthlyTrend,
      location_stats: locationStats
    }
  });
};

/**
 * Manually trigger feedback collection for a specific adhyayan
 * POST /api/v1/admin/feedback/trigger/:shibir_id
 */
export const triggerFeedbackCollection = async (req, res) => {
  const { shibir_id } = req.params;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  try {
    const result = await triggerFeedbackForAdhyayan(parseInt(shibir_id));

    return res.status(200).send({
      message: MSG_UPDATE_SUCCESSFUL,
      data: result
    });
  } catch (error) {
    throw new ApiError(400, error.message);
  }
};

/**
 * Get feedback collection statistics for an adhyayan
 * GET /api/v1/admin/feedback/stats/:shibir_id
 */
export const fetchFeedbackStats = async (req, res) => {
  const { shibir_id } = req.params;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  try {
    const stats = await getFeedbackCollectionStats(parseInt(shibir_id));

    return res.status(200).send({
      message: MSG_FETCH_SUCCESSFUL,
      data: stats
    });
  } catch (error) {
    throw new ApiError(400, error.message);
  }
};
