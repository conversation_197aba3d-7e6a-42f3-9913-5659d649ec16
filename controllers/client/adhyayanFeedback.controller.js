import { AdhyayanFeedback, ShibirDb, CardDb } from '../../models/associations.js';
import {
  validateFeedbackEligibility,
  validateFeedbackData,
  createFeedback,
  getEligibleAdhyayansForFeedback,
  hasFeedbackSubmitted
} from '../../helpers/adhyayanFeedback.helper.js';
import {
  MSG_FETCH_SUCCESSFUL,
  MSG_UPDATE_SUCCESSFUL
} from '../../config/constants.js';
import database from '../../config/database.js';
import ApiError from '../../utils/ApiError.js';

/**
 * Submit feedback for an adhyayan
 * POST /api/v1/feedback/submit
 */
export const submitFeedback = async (req, res) => {
  const {
    shibir_id,
    swadhay_karta_rating,
    personal_interaction_rating,
    swadhay_karta_suggestions,
    raj_adhyayan_interest,
    future_topics,
    loved_most,
    improvement_suggestions,
    food_rating,
    stay_rating
  } = req.body;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  const t = await database.transaction();
  req.transaction = t;

  try {
    // Validate eligibility
    await validateFeedbackEligibility(req.user.cardno, shibir_id);

    // Validate feedback data
    const validatedData = validateFeedbackData({
      swadhay_karta_rating,
      personal_interaction_rating,
      swadhay_karta_suggestions,
      raj_adhyayan_interest,
      future_topics,
      loved_most,
      improvement_suggestions,
      food_rating,
      stay_rating
    });

    // Create feedback
    const feedback = await createFeedback(
      req.user.cardno,
      shibir_id,
      validatedData,
      t
    );

    await t.commit();

    return res.status(201).send({
      message: 'Feedback submitted successfully',
      data: {
        id: feedback.id,
        submitted_at: feedback.submitted_at
      }
    });
  } catch (error) {
    await t.rollback();
    throw error;
  }
};

/**
 * Get list of adhyayans eligible for feedback
 * GET /api/v1/feedback/eligible
 */
export const getEligibleAdhyayans = async (req, res) => {
  const eligibleAdhyayans = await getEligibleAdhyayansForFeedback(req.user.cardno);

  const formattedAdhyayans = eligibleAdhyayans.map(adhyayan => ({
    id: adhyayan.id,
    name: adhyayan.name,
    speaker: adhyayan.speaker,
    start_date: adhyayan.start_date,
    end_date: adhyayan.end_date,
    location: adhyayan.location
  }));

  return res.status(200).send({
    message: MSG_FETCH_SUCCESSFUL,
    data: formattedAdhyayans
  });
};

/**
 * Check if feedback has been submitted for a specific adhyayan
 * GET /api/v1/feedback/status/:shibir_id
 */
export const getFeedbackStatus = async (req, res) => {
  const { shibir_id } = req.params;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  const hasSubmitted = await hasFeedbackSubmitted(req.user.cardno, parseInt(shibir_id));

  return res.status(200).send({
    message: MSG_FETCH_SUCCESSFUL,
    data: {
      shibir_id: parseInt(shibir_id),
      feedback_submitted: hasSubmitted
    }
  });
};

/**
 * Get user's submitted feedback for a specific adhyayan
 * GET /api/v1/feedback/:shibir_id
 */
export const getUserFeedback = async (req, res) => {
  const { shibir_id } = req.params;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  const feedback = await AdhyayanFeedback.findOne({
    where: {
      cardno: req.user.cardno,
      shibir_id: parseInt(shibir_id)
    },
    include: [
      {
        model: ShibirDb,
        attributes: ['id', 'name', 'speaker', 'start_date', 'end_date', 'location']
      }
    ]
  });

  if (!feedback) {
    throw new ApiError(404, 'Feedback not found');
  }

  return res.status(200).send({
    message: MSG_FETCH_SUCCESSFUL,
    data: feedback
  });
};
