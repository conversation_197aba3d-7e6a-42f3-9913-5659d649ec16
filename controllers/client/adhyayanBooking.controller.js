import {
  <PERSON>birDb,
  <PERSON><PERSON>BookingDb,
  AdhyayanFeedback
} from '../../models/associations.js';
import {
  STATUS_CONFIRMED,
  STATUS_PAYMENT_PENDING,
  TYPE_ADHYAYAN,
  ERR_BOOKING_NOT_FOUND,
  TYPE_GUEST_ADHYAYAN,
  STATUS_CANCELLED,
  STATUS_ADMIN_CANCELLED,
  ERR_BOOKING_ALREADY_CANCELLED,
  STATUS_DELETED,
  MSG_FETCH_SUCCESSFUL
} from '../../config/constants.js';
import {
  validateFeedbackEligibility,
  validateFeedbackData,
  createFeedback,
  getEligibleAdhyayansForFeedback,
  hasFeedbackSubmitted
} from '../../helpers/adhyayanFeedback.helper.js';
import { openAdhyayanSeat } from '../../helpers/adhyayanBooking.helper.js';
import { userCancelBooking } from '../../helpers/transactions.helper.js';
import { sendNotification } from '../../utils/sendNotification.js';
import database from '../../config/database.js';
import Sequelize from 'sequelize';
import moment from 'moment';
import sendMail from '../../utils/sendMail.js';
import ApiError from '../../utils/ApiError.js';

export const FetchAllShibir = async (req, res) => {
  const today = moment().format('YYYY-MM-DD');

  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.page_size) || 10;
  const offset = (page - 1) * pageSize;

  const shibirs = await ShibirDb.findAll({
    where: {
      start_date: {
        [Sequelize.Op.gt]: today
      },
      status: {
        [Sequelize.Op.ne]: STATUS_DELETED
      }
    },
    offset,
    limit: pageSize,
    order: [['start_date', 'ASC']]
  });

  const groupedByMonth = shibirs.reduce((acc, event) => {
    const month = event.month;
    if (!acc[month]) {
      acc[month] = [];
    }
    acc[month].push(event);
    return acc;
  }, {});

  const formattedResponse = {
    message: 'fetched results',
    data: Object.keys(groupedByMonth).map((month) => ({
      title: month,
      data: groupedByMonth[month]
    }))
  };

  return res.status(200).send(formattedResponse);
};

export const FetchBookedShibir = async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.page_size) || 10;
  const offset = (page - 1) * pageSize;

  const shibirs = await database.query(
    `
    SELECT t1.bookingid,
       t1.cardno,
       t1.bookedBy AS bookedBy,
       t4.issuedto AS name,
       t1.shibir_id,
       t2.location,
       t1.status,
       t2.name AS shibir_name,
       t2.speaker,
       t2.start_date,
       t2.end_date,
       COALESCE(t3.amount, 0) AS amount,
       t3.status AS transaction_status
    FROM shibir_booking_db t1
    JOIN shibir_db t2 ON t1.shibir_id = t2.id
    LEFT JOIN transactions t3 ON t1.bookingid = t3.bookingid
      AND t3.category IN (:category)
    LEFT JOIN card_db t4 ON t4.cardno = t1.cardno
    WHERE (t1.cardno = :cardno OR t1.bookedBy = :cardno)
    ORDER BY t2.start_date DESC
    LIMIT :limit
    OFFSET :offset;
    `,
    {
      replacements: {
        cardno: req.user.cardno,
        category: [TYPE_ADHYAYAN, TYPE_GUEST_ADHYAYAN],
        limit: pageSize,
        offset: offset
      },
      type: Sequelize.QueryTypes.SELECT
    }
  );

  const currentDate = new Date();
  shibirs.forEach((shibir) => {
    shibir.showFeedback =
      new Date(shibir.end_date) < currentDate &&
      shibir.status === STATUS_CONFIRMED;
  });

  return res.status(200).send({ data: shibirs });
};

export const CancelShibir = async (req, res) => {
  const { bookingid } = req.body;

  const t = await database.transaction();
  req.transaction = t;

  const booking = await ShibirBookingDb.findOne({
    where: {
      bookingid: bookingid,
      [Sequelize.Op.or]: [
        { cardno: req.user.cardno },
        { bookedBy: req.user.cardno }
      ]
    }
  });

  if (!booking) {
    throw new ApiError(404, ERR_BOOKING_NOT_FOUND);
  }

  if ([STATUS_CANCELLED, STATUS_ADMIN_CANCELLED].includes(booking.status)) {
    throw new ApiError(400, ERR_BOOKING_ALREADY_CANCELLED);
  }

  const adhyayan = await ShibirDb.findOne({
    where: { id: booking.shibir_id }
  });

  if ([STATUS_CONFIRMED, STATUS_PAYMENT_PENDING].includes(booking.status)) {
    await openAdhyayanSeat(adhyayan, req.user.username, t);
  }

  await userCancelBooking(req.user, booking, t);
  await t.commit();

  sendMail({
    email: req.user.email,
    subject: 'Raj Adhyayan Booking Cancelled',
    template: 'rajAdhyayanCancellation',
    context: {
      name: req.user.issuedto,
      adhyayanName: adhyayan.name
    }
  });

  // Call the refactored utility function
  if (req.user.pushToken) {
    await sendNotification([
      {
        token: req.user.pushToken,
        title: 'Booking Cancelled',
        body: `Your booking for "${adhyayan.name}" has been cancelled.`,
        screen: 'CancelledBookings',
        data: {
          shibir_id: adhyayan.id,
          status: 'cancelled'
        }
      }
    ]);
  }

  return res.status(200).send({ message: 'Shibir booking cancelled' });
};

export const FetchShibirInRange = async (req, res) => {
  const { start_date } = req.query;
  let { end_date } = req.query;

  const startDateObj = new Date(start_date);
  if (!end_date) {
    const endDateObj = new Date(startDateObj);
    endDateObj.setDate(startDateObj.getDate() + 15); // Add 15 days
    end_date = endDateObj.toISOString().split('T')[0]; // Format the new end_date as YYYY-MM-DD
  }

  const whereCondition = {
    start_date: {
      [Sequelize.Op.gte]: start_date
    }
  };

  if (end_date) {
    whereCondition.start_date[Sequelize.Op.lte] = end_date;
    whereCondition.end_date = {
      [Sequelize.Op.gte]: start_date,
      [Sequelize.Op.lte]: end_date
    };
  }

  const shibirs = await ShibirDb.findAll({
    where: whereCondition,
    order: [['start_date', 'ASC']]
  });

  return res.status(200).send({ data: shibirs });
};

export const FetchShibirById = async (req, res) => {
  const { id } = req.params;

  const shibir = await ShibirDb.findOne({
    where: {
      id: id
    }
  });

  if (!shibir) throw new ApiError(404, 'Shibir not found');

  return res.status(200).send({ data: shibir });
};

/**
 * Submit feedback for an adhyayan
 * POST /api/v1/adhyayan/feedback
 */
export const submitFeedback = async (req, res) => {
  const {
    shibir_id,
    swadhay_karta_rating,
    personal_interaction_rating,
    swadhay_karta_suggestions,
    raj_adhyayan_interest,
    future_topics,
    loved_most,
    improvement_suggestions,
    food_rating,
    stay_rating
  } = req.body;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  const t = await database.transaction();
  req.transaction = t;

  await validateFeedbackEligibility(req.user.cardno, shibir_id);

  const validatedData = validateFeedbackData({
    swadhay_karta_rating,
    personal_interaction_rating,
    swadhay_karta_suggestions,
    raj_adhyayan_interest,
    future_topics,
    loved_most,
    improvement_suggestions,
    food_rating,
    stay_rating
  });

  const feedback = await createFeedback(
    req.user.cardno,
    shibir_id,
    validatedData,
    t
  );

  await t.commit();

  return res.status(201).send({
    message: 'Feedback submitted successfully',
    data: {
      id: feedback.id,
      submitted_at: feedback.submitted_at
    }
  });
};

/**
 * Get user's submitted feedback for a specific adhyayan
 * GET /api/v1/adhyayan/feedback/:shibir_id
 */
export const getUserFeedback = async (req, res) => {
  const { shibir_id } = req.params;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  const feedback = await AdhyayanFeedback.findOne({
    where: {
      cardno: req.user.cardno,
      shibir_id: parseInt(shibir_id)
    },
    include: [
      {
        model: ShibirDb,
        attributes: [
          'id',
          'name',
          'speaker',
          'start_date',
          'end_date',
          'location'
        ]
      }
    ]
  });

  if (!feedback) {
    throw new ApiError(404, 'Feedback not found');
  }

  return res.status(200).send({
    message: MSG_FETCH_SUCCESSFUL,
    data: feedback
  });
};
