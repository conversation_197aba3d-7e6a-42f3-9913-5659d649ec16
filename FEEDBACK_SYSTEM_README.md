# Adhyayan Feedback System

## Overview

The Adhyayan Feedback System is a comprehensive feedback collection feature that automatically triggers after every adhyayan (shibir) session ends. It allows users to provide detailed feedback about their experience and helps administrators analyze and improve future sessions.

## Features

### User Features
- **Automatic Feedback Triggers**: Users receive notifications to submit feedback after adhyayan sessions end
- **Comprehensive Feedback Form**: 9-field feedback form covering all aspects of the experience
- **Eligibility Validation**: Only confirmed participants can submit feedback
- **Duplicate Prevention**: One feedback per user per adhyayan
- **Feedback History**: Users can view their previously submitted feedback

### Admin Features
- **Feedback Analytics**: Comprehensive dashboard with statistics and trends
- **Detailed Reports**: Export detailed feedback reports for analysis
- **Manual Triggers**: Manually trigger feedback collection for specific sessions
- **Response Rate Tracking**: Monitor feedback collection statistics
- **Location-wise Analysis**: Compare feedback across different locations

## Database Schema

### AdhyayanFeedback Model
```sql
CREATE TABLE adhyayan_feedback (
  id INT PRIMARY KEY AUTO_INCREMENT,
  shibir_id INT NOT NULL,
  cardno VARCHAR(255) NOT NULL,
  swadhay_karta_rating INT NOT NULL (1-5),
  personal_interaction_rating INT NOT NULL (1-5),
  swadhay_karta_suggestions TEXT,
  raj_adhyayan_interest BOOLEAN NOT NULL,
  future_topics TEXT,
  loved_most TEXT,
  improvement_suggestions TEXT,
  food_rating INT NOT NULL (1-5),
  stay_rating INT NOT NULL (1-5),
  submitted_at DATETIME NOT NULL,
  updatedBy VARCHAR(255) NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  
  UNIQUE KEY unique_feedback_per_user_per_adhyayan (shibir_id, cardno),
  FOREIGN KEY (shibir_id) REFERENCES shibir_db(id),
  FOREIGN KEY (cardno) REFERENCES card_db(cardno)
);
```

## API Endpoints

### Client Endpoints

#### Submit Feedback
```
POST /api/v1/feedback/submit
```
**Body:**
```json
{
  "shibir_id": 123,
  "swadhay_karta_rating": 5,
  "personal_interaction_rating": 4,
  "swadhay_karta_suggestions": "Great session, very insightful",
  "raj_adhyayan_interest": true,
  "future_topics": "More on meditation techniques",
  "loved_most": "The practical examples shared",
  "improvement_suggestions": "Maybe longer sessions",
  "food_rating": 4,
  "stay_rating": 5
}
```

#### Get Eligible Adhyayans
```
GET /api/v1/feedback/eligible
```

#### Check Feedback Status
```
GET /api/v1/feedback/status/:shibir_id
```

#### Get User Feedback
```
GET /api/v1/feedback/:shibir_id
```

### Admin Endpoints

#### Get Feedback Analytics
```
GET /api/v1/admin/feedback/analytics?period=6months
```

#### Get Feedback Summary
```
GET /api/v1/admin/feedback/summary?start_date=2024-01-01&end_date=2024-12-31&location=Research Centre
```

#### Get Adhyayan Feedback
```
GET /api/v1/admin/feedback/adhyayan/:shibir_id?page=1&page_size=20
```

#### Get Feedback Report
```
GET /api/v1/admin/feedback/report/:shibir_id
```

#### Trigger Feedback Collection
```
POST /api/v1/admin/feedback/trigger/:shibir_id
```

#### Get Feedback Statistics
```
GET /api/v1/admin/feedback/stats/:shibir_id
```

## Feedback Form Fields

1. **Swadhay Karta Rating** (1-5): How would you rate the Swadhay Karta's session?
2. **Personal Interaction Rating** (1-5): How was your personal interaction with the swadhay karta?
3. **Swadhay Karta Suggestions** (text): Any suggestions for the swadhay karta to improve their skills/activity?
4. **Raj Adhyayan Interest** (boolean): Would you be interested to attend Raj Adhyayan in the future?
5. **Future Topics** (text): Any topics would you like us to take Raj Adhyayan on in future?
6. **Loved Most** (text): What did you love the most about this Raj Adhyayan?
7. **Improvement Suggestions** (text): Any other scope of improvement?
8. **Food Rating** (1-5): How was the food at the bhojanalay?
9. **Stay Rating** (1-5): How was your stay at Research Centre?

## Automated Triggers

### Daily Cron Job
- Runs daily at 10:00 AM
- Identifies adhyayans that ended the previous day
- Sends feedback requests to all confirmed participants
- Skips users who have already submitted feedback

### Notification Methods
- **Email**: Detailed feedback request with session information
- **Push Notification**: Brief reminder to submit feedback

## Validation Rules

### Rating Fields
- Must be integers between 1 and 5
- Required fields: swadhay_karta_rating, personal_interaction_rating, food_rating, stay_rating

### Text Fields
- Maximum 1000 characters each
- Optional fields: swadhay_karta_suggestions, future_topics, loved_most, improvement_suggestions

### Business Rules
- Only confirmed participants can submit feedback
- Adhyayan must have ended before feedback can be submitted
- One feedback per user per adhyayan (enforced by unique constraint)
- User must have a valid booking with status 'confirmed' or 'cash completed'

## Installation & Setup

### 1. Run Migration
```bash
npx sequelize-cli db:migrate
```

### 2. Update App Configuration
The feedback routes are automatically included in app.js:
- Client routes: `/api/v1/feedback/*`
- Admin routes: `/api/v1/admin/feedback/*`

### 3. Cron Job Setup
The feedback trigger cron job is automatically configured in cron.js and runs daily at 10:00 AM.

## Testing

Run the feedback system tests:
```bash
npm test tests/controllers/client/adhyayanFeedback.controller.test.js
```

## Security & Authorization

### Client Endpoints
- Require valid card authentication via `validateCard` middleware
- Users can only access their own feedback data

### Admin Endpoints
- Require admin authentication via `auth` middleware
- Restricted to users with `ROLE_SUPER_ADMIN` or `ROLE_ADHYAYAN_ADMIN` roles
- Input validation via custom validation middleware

## Error Handling

Common error responses:
- `400`: Invalid input data or business rule violations
- `403`: User not eligible to submit feedback
- `404`: Adhyayan or feedback not found
- `500`: Server errors

## Performance Considerations

### Database Indexes
- Unique index on (shibir_id, cardno) for duplicate prevention
- Individual indexes on shibir_id, cardno, and submitted_at for query optimization

### Pagination
- Admin endpoints support pagination with configurable page sizes
- Default page size: 20 records

## Future Enhancements

1. **Feedback Reminders**: Send follow-up reminders for pending feedback
2. **Anonymous Feedback**: Option for anonymous feedback submission
3. **Feedback Templates**: Pre-defined feedback templates for different session types
4. **Real-time Analytics**: Live dashboard updates for feedback statistics
5. **Export Functionality**: CSV/Excel export for detailed reports
