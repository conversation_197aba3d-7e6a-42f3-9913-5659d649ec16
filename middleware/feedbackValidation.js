import ApiError from '../utils/ApiError.js';
import catchAsync from '../utils/CatchAsync.js';

/**
 * Validates feedback submission data
 */
export const validateFeedbackSubmission = catchAsync(async (req, res, next) => {
  const {
    shibir_id,
    swadhay_karta_rating,
    personal_interaction_rating,
    swadhay_karta_suggestions,
    raj_adhyayan_interest,
    future_topics,
    loved_most,
    improvement_suggestions,
    food_rating,
    stay_rating
  } = req.body;

  // Validate required fields
  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID (shibir_id) is required');
  }

  if (!Number.isInteger(parseInt(shibir_id)) || parseInt(shibir_id) <= 0) {
    throw new ApiError(400, 'Adhyayan ID must be a positive integer');
  }

  // Validate required rating fields
  const requiredRatings = [
    { field: 'swadhay_karta_rating', value: swadhay_karta_rating, label: 'Swadhay Karta Rating' },
    { field: 'personal_interaction_rating', value: personal_interaction_rating, label: 'Personal Interaction Rating' },
    { field: 'food_rating', value: food_rating, label: 'Food Rating' },
    { field: 'stay_rating', value: stay_rating, label: 'Stay Rating' }
  ];

  for (const rating of requiredRatings) {
    if (rating.value === undefined || rating.value === null) {
      throw new ApiError(400, `${rating.label} is required`);
    }

    const ratingValue = parseInt(rating.value);
    if (!Number.isInteger(ratingValue) || ratingValue < 1 || ratingValue > 5) {
      throw new ApiError(400, `${rating.label} must be an integer between 1 and 5`);
    }
  }

  // Validate boolean field
  if (raj_adhyayan_interest === undefined || raj_adhyayan_interest === null) {
    throw new ApiError(400, 'Interest in future Raj Adhyayan (raj_adhyayan_interest) is required');
  }

  if (typeof raj_adhyayan_interest !== 'boolean' && 
      raj_adhyayan_interest !== 'true' && 
      raj_adhyayan_interest !== 'false' &&
      raj_adhyayan_interest !== true &&
      raj_adhyayan_interest !== false) {
    throw new ApiError(400, 'Interest in future Raj Adhyayan must be true or false');
  }

  // Validate optional text fields length
  const textFields = [
    { field: 'swadhay_karta_suggestions', value: swadhay_karta_suggestions, label: 'Swadhay Karta Suggestions' },
    { field: 'future_topics', value: future_topics, label: 'Future Topics' },
    { field: 'loved_most', value: loved_most, label: 'What You Loved Most' },
    { field: 'improvement_suggestions', value: improvement_suggestions, label: 'Improvement Suggestions' }
  ];

  for (const textField of textFields) {
    if (textField.value !== undefined && textField.value !== null) {
      if (typeof textField.value !== 'string') {
        throw new ApiError(400, `${textField.label} must be a string`);
      }
      
      if (textField.value.length > 1000) {
        throw new ApiError(400, `${textField.label} must be less than 1000 characters`);
      }
    }
  }

  // Sanitize and normalize data
  req.body.shibir_id = parseInt(shibir_id);
  req.body.swadhay_karta_rating = parseInt(swadhay_karta_rating);
  req.body.personal_interaction_rating = parseInt(personal_interaction_rating);
  req.body.food_rating = parseInt(food_rating);
  req.body.stay_rating = parseInt(stay_rating);
  req.body.raj_adhyayan_interest = Boolean(raj_adhyayan_interest === true || raj_adhyayan_interest === 'true');

  // Trim and sanitize text fields
  if (swadhay_karta_suggestions) {
    req.body.swadhay_karta_suggestions = swadhay_karta_suggestions.trim();
  }
  if (future_topics) {
    req.body.future_topics = future_topics.trim();
  }
  if (loved_most) {
    req.body.loved_most = loved_most.trim();
  }
  if (improvement_suggestions) {
    req.body.improvement_suggestions = improvement_suggestions.trim();
  }

  next();
});

/**
 * Validates admin feedback query parameters
 */
export const validateFeedbackQuery = catchAsync(async (req, res, next) => {
  const { page, page_size, start_date, end_date, location } = req.query;

  // Validate pagination parameters
  if (page !== undefined) {
    const pageNum = parseInt(page);
    if (!Number.isInteger(pageNum) || pageNum < 1) {
      throw new ApiError(400, 'Page must be a positive integer');
    }
    req.query.page = pageNum;
  }

  if (page_size !== undefined) {
    const pageSizeNum = parseInt(page_size);
    if (!Number.isInteger(pageSizeNum) || pageSizeNum < 1 || pageSizeNum > 100) {
      throw new ApiError(400, 'Page size must be between 1 and 100');
    }
    req.query.page_size = pageSizeNum;
  }

  // Validate date parameters
  if (start_date !== undefined) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(start_date)) {
      throw new ApiError(400, 'Start date must be in YYYY-MM-DD format');
    }
    
    const startDateObj = new Date(start_date);
    if (isNaN(startDateObj.getTime())) {
      throw new ApiError(400, 'Invalid start date');
    }
  }

  if (end_date !== undefined) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(end_date)) {
      throw new ApiError(400, 'End date must be in YYYY-MM-DD format');
    }
    
    const endDateObj = new Date(end_date);
    if (isNaN(endDateObj.getTime())) {
      throw new ApiError(400, 'Invalid end date');
    }

    // Check if end date is after start date
    if (start_date && new Date(end_date) < new Date(start_date)) {
      throw new ApiError(400, 'End date must be after start date');
    }
  }

  // Validate location parameter
  if (location !== undefined) {
    if (typeof location !== 'string' || location.trim().length === 0) {
      throw new ApiError(400, 'Location must be a non-empty string');
    }
    req.query.location = location.trim();
  }

  next();
});

/**
 * Validates shibir_id parameter
 */
export const validateShibirIdParam = catchAsync(async (req, res, next) => {
  const { shibir_id } = req.params;

  if (!shibir_id) {
    throw new ApiError(400, 'Adhyayan ID is required');
  }

  const shibirIdNum = parseInt(shibir_id);
  if (!Number.isInteger(shibirIdNum) || shibirIdNum <= 0) {
    throw new ApiError(400, 'Adhyayan ID must be a positive integer');
  }

  req.params.shibir_id = shibirIdNum;
  next();
});
