import request from 'supertest';
import app from '../../../app.js';
import { AdhyayanFeedback, ShibirDb, ShibirBookingDb, CardDb } from '../../../models/associations.js';
import database from '../../../config/database.js';
import { STATUS_CONFIRMED } from '../../../config/constants.js';
import moment from 'moment';

describe('Adhyayan Feedback Controller', () => {
  let testCard, testAdhyayan, testBooking;

  beforeAll(async () => {
    await database.authenticate();
  });

  beforeEach(async () => {
    // Create test data
    testCard = await CardDb.create({
      cardno: 'TEST001',
      issuedto: 'Test User',
      gender: 'M',
      mobno: 9876543210,
      email: '<EMAIL>',
      center: 'Test Center',
      res_status: 'MUMUKSHU',
      status: 'onprem',
      updatedBy: 'TEST'
    });

    testAdhyayan = await <PERSON>birDb.create({
      name: '<PERSON> <PERSON>',
      speaker: 'Test Speaker',
      month: 'January',
      start_date: moment().subtract(5, 'days').format('YYYY-MM-DD'),
      end_date: moment().subtract(2, 'days').format('YYYY-MM-DD'),
      location: 'Test Location',
      total_seats: 50,
      available_seats: 45,
      amount: 100,
      updatedBy: 'TEST'
    });

    testBooking = await ShibirBookingDb.create({
      bookingid: 'TEST_BOOKING_001',
      cardno: testCard.cardno,
      shibir_id: testAdhyayan.id,
      status: STATUS_CONFIRMED,
      updatedBy: 'TEST'
    });
  });

  afterEach(async () => {
    // Clean up test data
    await AdhyayanFeedback.destroy({ where: {} });
    await ShibirBookingDb.destroy({ where: {} });
    await ShibirDb.destroy({ where: {} });
    await CardDb.destroy({ where: {} });
  });

  afterAll(async () => {
    await database.close();
  });

  describe('POST /api/v1/feedback/submit', () => {
    it('should submit feedback successfully', async () => {
      const feedbackData = {
        shibir_id: testAdhyayan.id,
        swadhay_karta_rating: 5,
        personal_interaction_rating: 4,
        swadhay_karta_suggestions: 'Great session, very insightful',
        raj_adhyayan_interest: true,
        future_topics: 'More on meditation techniques',
        loved_most: 'The practical examples shared',
        improvement_suggestions: 'Maybe longer sessions',
        food_rating: 4,
        stay_rating: 5
      };

      const response = await request(app)
        .post('/api/v1/feedback/submit')
        .query({ cardno: testCard.cardno })
        .send(feedbackData)
        .expect(201);

      expect(response.body.message).toBe('Feedback submitted successfully');
      expect(response.body.data.id).toBeDefined();

      // Verify feedback was saved
      const savedFeedback = await AdhyayanFeedback.findOne({
        where: {
          cardno: testCard.cardno,
          shibir_id: testAdhyayan.id
        }
      });

      expect(savedFeedback).toBeTruthy();
      expect(savedFeedback.swadhay_karta_rating).toBe(5);
      expect(savedFeedback.raj_adhyayan_interest).toBe(true);
    });

    it('should reject invalid rating values', async () => {
      const feedbackData = {
        shibir_id: testAdhyayan.id,
        swadhay_karta_rating: 6, // Invalid rating
        personal_interaction_rating: 4,
        raj_adhyayan_interest: true,
        food_rating: 4,
        stay_rating: 5
      };

      await request(app)
        .post('/api/v1/feedback/submit')
        .query({ cardno: testCard.cardno })
        .send(feedbackData)
        .expect(400);
    });

    it('should prevent duplicate feedback submission', async () => {
      const feedbackData = {
        shibir_id: testAdhyayan.id,
        swadhay_karta_rating: 5,
        personal_interaction_rating: 4,
        raj_adhyayan_interest: true,
        food_rating: 4,
        stay_rating: 5
      };

      // Submit first feedback
      await request(app)
        .post('/api/v1/feedback/submit')
        .query({ cardno: testCard.cardno })
        .send(feedbackData)
        .expect(201);

      // Try to submit again
      await request(app)
        .post('/api/v1/feedback/submit')
        .query({ cardno: testCard.cardno })
        .send(feedbackData)
        .expect(400);
    });
  });

  describe('GET /api/v1/feedback/eligible', () => {
    it('should return eligible adhyayans for feedback', async () => {
      const response = await request(app)
        .get('/api/v1/feedback/eligible')
        .query({ cardno: testCard.cardno })
        .expect(200);

      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].id).toBe(testAdhyayan.id);
      expect(response.body.data[0].name).toBe('Test Raj Adhyayan');
    });

    it('should not return adhyayans with existing feedback', async () => {
      // Submit feedback first
      await AdhyayanFeedback.create({
        cardno: testCard.cardno,
        shibir_id: testAdhyayan.id,
        swadhay_karta_rating: 5,
        personal_interaction_rating: 4,
        raj_adhyayan_interest: true,
        food_rating: 4,
        stay_rating: 5,
        updatedBy: testCard.cardno
      });

      const response = await request(app)
        .get('/api/v1/feedback/eligible')
        .query({ cardno: testCard.cardno })
        .expect(200);

      expect(response.body.data).toHaveLength(0);
    });
  });

  describe('GET /api/v1/feedback/status/:shibir_id', () => {
    it('should return feedback status', async () => {
      const response = await request(app)
        .get(`/api/v1/feedback/status/${testAdhyayan.id}`)
        .query({ cardno: testCard.cardno })
        .expect(200);

      expect(response.body.data.shibir_id).toBe(testAdhyayan.id);
      expect(response.body.data.feedback_submitted).toBe(false);
    });

    it('should return true when feedback is submitted', async () => {
      // Submit feedback first
      await AdhyayanFeedback.create({
        cardno: testCard.cardno,
        shibir_id: testAdhyayan.id,
        swadhay_karta_rating: 5,
        personal_interaction_rating: 4,
        raj_adhyayan_interest: true,
        food_rating: 4,
        stay_rating: 5,
        updatedBy: testCard.cardno
      });

      const response = await request(app)
        .get(`/api/v1/feedback/status/${testAdhyayan.id}`)
        .query({ cardno: testCard.cardno })
        .expect(200);

      expect(response.body.data.feedback_submitted).toBe(true);
    });
  });

  describe('GET /api/v1/feedback/:shibir_id', () => {
    it('should return user feedback for adhyayan', async () => {
      // Submit feedback first
      const feedback = await AdhyayanFeedback.create({
        cardno: testCard.cardno,
        shibir_id: testAdhyayan.id,
        swadhay_karta_rating: 5,
        personal_interaction_rating: 4,
        swadhay_karta_suggestions: 'Great session',
        raj_adhyayan_interest: true,
        food_rating: 4,
        stay_rating: 5,
        updatedBy: testCard.cardno
      });

      const response = await request(app)
        .get(`/api/v1/feedback/${testAdhyayan.id}`)
        .query({ cardno: testCard.cardno })
        .expect(200);

      expect(response.body.data.id).toBe(feedback.id);
      expect(response.body.data.swadhay_karta_rating).toBe(5);
      expect(response.body.data.swadhay_karta_suggestions).toBe('Great session');
    });

    it('should return 404 when feedback not found', async () => {
      await request(app)
        .get(`/api/v1/feedback/${testAdhyayan.id}`)
        .query({ cardno: testCard.cardno })
        .expect(404);
    });
  });
});
